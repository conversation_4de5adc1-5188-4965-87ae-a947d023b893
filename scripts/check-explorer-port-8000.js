async function main() {
  console.log("🌐 Checking DTC Token on Explorer Port 8000");
  console.log("=".repeat(60));
  
  const contractAddress = "******************************************";
  const [deployer] = await ethers.getSigners();
  
  console.log("📋 Explorer Info:");
  console.log("- Explorer URL: http://localhost:8000/");
  console.log("- Network:", hre.network.name);
  console.log("- Chain ID:", (await ethers.provider.getNetwork()).chainId);
  console.log("- Contract Address:", contractAddress);
  console.log("- Deployer Address:", deployer.address);
  
  // Kết nối với contract
  const DATACOIN = await ethers.getContractFactory("DATACOIN");
  const token = DATACOIN.attach(contractAddress);
  
  console.log("\n📄 DTC Token Information:");
  const name = await token.name();
  const symbol = await token.symbol();
  const decimals = await token.decimals();
  const totalSupply = await token.totalSupply();
  
  console.log("- Name:", name);
  console.log("- Symbol:", symbol);
  console.log("- Decimals:", decimals.toString());
  console.log("- Total Supply:", ethers.formatEther(totalSupply));
  
  console.log("\n🔗 Correct Explorer URLs (Port 8000):");
  console.log("1. Contract Page:");
  console.log(`   http://localhost:8000/address/${contractAddress}`);
  console.log("2. Deployer Address:");
  console.log(`   http://localhost:8000/address/${deployer.address}`);
  console.log("3. Latest Blocks:");
  console.log(`   http://localhost:8000/blocks`);
  console.log("4. Latest Transactions:");
  console.log(`   http://localhost:8000/txs`);
  
  // Tạo một transaction mới để test
  console.log("\n🧪 Creating New Test Transaction:");
  try {
    const testRecipient = "******************************************";
    const testAmount = ethers.parseEther("50");
    
    console.log(`- Sending 50 DTC to ${testRecipient}...`);
    
    const tx = await token.transfer(testRecipient, testAmount, {
      gasLimit: 100000
    });
    
    console.log(`- Transaction Hash: ${tx.hash}`);
    console.log(`- Waiting for confirmation...`);
    
    const receipt = await tx.wait();
    
    console.log(`- ✅ Confirmed in Block: ${receipt.blockNumber}`);
    console.log(`- Gas Used: ${receipt.gasUsed.toString()}`);
    
    // URLs để check trên explorer port 8000
    console.log(`\n🔍 Check these URLs on your explorer (Port 8000):`);
    console.log(`1. Transaction: http://localhost:8000/tx/${tx.hash}`);
    console.log(`2. Block: http://localhost:8000/block/${receipt.blockNumber}`);
    console.log(`3. Contract: http://localhost:8000/address/${contractAddress}`);
    console.log(`4. Recipient: http://localhost:8000/address/${testRecipient}`);
    
  } catch (error) {
    console.log(`❌ Test transaction failed: ${error.message}`);
  }
  
  // Tạo thêm một vài transactions nữa
  console.log("\n🚀 Creating Multiple Transactions for Explorer:");
  
  const recipients = [
    "******************************************",
    "******************************************", 
    "******************************************"
  ];
  
  const amounts = ["25", "75", "100"];
  
  for (let i = 0; i < recipients.length; i++) {
    try {
      const transferAmount = ethers.parseEther(amounts[i]);
      const tx = await token.transfer(recipients[i], transferAmount, {
        gasLimit: 100000
      });
      
      console.log(`${i + 1}. ${amounts[i]} DTC → ${recipients[i].slice(0, 10)}... | TX: ${tx.hash.slice(0, 10)}...`);
      
      const receipt = await tx.wait();
      console.log(`   ✅ Block: ${receipt.blockNumber} | Explorer: http://localhost:8000/tx/${tx.hash}`);
      
      // Delay giữa các transaction
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.log(`${i + 1}. ❌ Failed: ${error.message}`);
    }
  }
  
  // Kiểm tra balances hiện tại
  console.log("\n💰 Current Token Balances:");
  const allAddresses = [deployer.address, ...recipients, "******************************************"];
  
  for (const addr of allAddresses) {
    try {
      const balance = await token.balanceOf(addr);
      console.log(`${addr}: ${ethers.formatEther(balance)} DTC`);
    } catch (error) {
      console.log(`${addr}: ❌ Error reading balance`);
    }
  }
  
  // Network info
  const blockNumber = await ethers.provider.getBlockNumber();
  console.log(`\n📊 Network Status:`);
  console.log(`- Current Block: ${blockNumber}`);
  console.log(`- RPC URL: http://127.0.0.1:8545`);
  console.log(`- Explorer URL: http://localhost:8000/`);
  console.log(`- Chain ID: 9000`);
  
  console.log("\n💡 How to View DTC on Explorer:");
  console.log("1. Go to: http://localhost:8000/");
  console.log("2. Search for contract address: " + contractAddress);
  console.log("3. Or search for any transaction hash from above");
  console.log("4. Look for 'Token Transfers' or 'Internal Transactions' section");
  console.log("5. If explorer supports it, add token manually with:");
  console.log(`   - Address: ${contractAddress}`);
  console.log(`   - Symbol: DTC`);
  console.log(`   - Decimals: 18`);
  
  console.log("\n📝 Manual Token Addition Info:");
  console.log(`Contract Address: ${contractAddress}`);
  console.log(`Token Name: DATACOIN`);
  console.log(`Token Symbol: DTC`);
  console.log(`Decimals: 18`);
  console.log(`Total Supply: ${ethers.formatEther(totalSupply)} DTC`);
  
  console.log("\n🎉 Check completed! Now check your explorer at http://localhost:8000/");
}

main().catch((error) => {
  console.error("💥 Check failed:", error);
  process.exitCode = 1;
});
